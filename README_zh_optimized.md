# LLaMA Factory - 统一高效的大语言模型微调框架

![LLaMA Factory Logo](assets/logo.png)

[![GitHub Repo stars](https://img.shields.io/github/stars/hiyouga/LLaMA-Factory?style=social)](https://github.com/hiyouga/LLaMA-Factory/stargazers)
[![GitHub last commit](https://img.shields.io/github/last-commit/hiyouga/LLaMA-Factory)](https://github.com/hiyouga/LLaMA-Factory/commits/main)
[![GitHub contributors](https://img.shields.io/github/contributors/hiyouga/LLaMA-Factory?color=orange)](https://github.com/hiyouga/LLaMA-Factory/graphs/contributors)
[![GitHub workflow](https://github.com/hiyouga/LLaMA-Factory/actions/workflows/tests.yml/badge.svg)](https://github.com/hiyouga/LLaMA-Factory/actions/workflows/tests.yml)
[![PyPI](https://img.shields.io/pypi/v/llamafactory)](https://pypi.org/project/llamafactory/)
[![Citation](https://img.shields.io/badge/citation-651-green)](https://scholar.google.com/scholar?cites=12620864006390196564)
[![Docker Pulls](https://img.shields.io/docker/pulls/hiyouga/llamafactory)](https://hub.docker.com/r/hiyouga/llamafactory/tags)

## 🌟 项目概述

LLaMA Factory 是一个统一高效的大语言模型微调框架，支持 100+ 种语言模型的微调。该项目已获得**亚马逊**、**英伟达**、**阿里云**等知名企业的应用认可。

### 🎯 核心特性

- **🔥 零代码微调**：通过命令行和 Web UI 轻松微调百余种大模型
- **⚡ 高效训练**：支持多种精度和优化算法，显著提升训练效率
- **🚀 极速推理**：基于 vLLM 或 SGLang 的 OpenAI 风格 API
- **📊 实验监控**：集成 LlamaBoard、TensorBoard、Wandb 等监控工具

### 🏆 获得认可

获得[亚马逊](https://aws.amazon.com/cn/blogs/china/a-one-stop-code-free-model-fine-tuning-deployment-platform-based-on-sagemaker-and-llama-factory/)、[英伟达](https://developer.nvidia.cn/rtx/ai-toolkit)、[阿里云](https://help.aliyun.com/zh/pai/use-cases/fine-tune-a-llama-3-model-with-llama-factory)等的应用。

## 📚 目录

1. [项目特色](#1-项目特色)
2. [支持模型](#2-支持模型)
3. [训练方法](#3-训练方法)
4. [数据集](#4-数据集)
5. [环境要求](#5-环境要求)
6. [安装指南](#6-安装指南)
7. [快速开始](#7-快速开始)
8. [高级功能](#8-高级功能)
9. [部署指南](#9-部署指南)
10. [常见问题](#10-常见问题)

## 1. 项目特色

### 1.1 多种模型支持
- **主流模型**：LLaMA、LLaVA、Mistral、Mixtral-MoE、Qwen、Qwen2-VL、DeepSeek、Yi、Gemma、ChatGLM、Phi 等
- **最新适配**：Day-0 支持最新模型（如 Qwen3、Qwen2.5-VL、Gemma 3、GLM-4.1V、InternLM 3、MiniCPM-o-2.6）

### 1.2 集成训练方法
- **预训练**：增量预训练、多模态预训练
- **监督微调**：指令监督微调、多模态指令微调
- **偏好对齐**：PPO、DPO、KTO、ORPO、SimPO 训练

### 1.3 多种精度支持
- **全精度**：16 比特全参数微调
- **参数高效**：冻结微调、LoRA 微调
- **量化训练**：基于 AQLM/AWQ/GPTQ/LLM.int8/HQQ/EETQ 的 2/3/4/5/6/8 比特 QLoRA 微调

### 1.4 先进算法
- **优化器**：[GaLore](https://github.com/jiaweizzhao/GaLore)、[BAdam](https://github.com/Ledzy/BAdam)、[APOLLO](https://github.com/zhuhanqing/APOLLO)、[Adam-mini](https://github.com/zyushun/Adam-mini)、[Muon](https://github.com/KellerJordan/Muon)
- **微调技术**：DoRA、LongLoRA、LLaMA Pro、Mixture-of-Depths、LoRA+、LoftQ、PiSSA

### 1.5 实用技巧
- **加速技术**：[FlashAttention-2](https://github.com/Dao-AILab/flash-attention)、[Unsloth](https://github.com/unslothai/unsloth)、[Liger Kernel](https://github.com/linkedin/Liger-Kernel)
- **训练优化**：RoPE scaling、NEFTune、rsLoRA

### 1.6 广泛任务支持
- **对话任务**：多轮对话、工具调用
- **多模态**：图像理解、视觉定位、视频识别、语音理解

## 2. 支持模型

### 2.1 文本模型

| 模型系列 | 参数规模 | 模板 |
|---------|---------|------|
| **LLaMA 系列** |
| ├─ Llama | 7B/13B/33B/65B | - |
| ├─ Llama 2 | 7B/13B/70B | llama2 |
| ├─ Llama 3-3.3 | 1B/3B/8B/70B | llama3 |
| └─ Llama 4 | 109B/402B | llama4 |
| **Qwen 系列** |
| ├─ Qwen (1-2.5) | 0.5B-110B | qwen |
| ├─ Qwen3 | 0.6B-235B | qwen3 |
| └─ Qwen2-Audio | 7B | qwen2_audio |
| **其他主流模型** |
| ├─ ChatGLM3/GLM-4 | 6B/9B/32B | chatglm3/glm4 |
| ├─ DeepSeek | 7B-671B | deepseek/deepseekr1 |
| ├─ Gemma/Gemma 2/3 | 1B-27B | gemma/gemma2/gemma3 |
| └─ Phi-3/4 | 4B/14B | phi/phi4 |

### 2.2 多模态模型

| 模型系列 | 参数规模 | 支持模态 | 模板 |
|---------|---------|----------|------|
| **视觉理解** |
| ├─ LLaVA-1.5/NeXT | 7B-110B | 图像+文本 | llava/llava_next |
| ├─ Qwen2-VL/2.5-VL | 2B-72B | 图像+视频+文本 | qwen2_vl |
| └─ InternVL 2.5-3 | 1B-78B | 图像+文本 | intern_vl |
| **音频理解** |
| ├─ Qwen2-Audio | 7B | 音频+文本 | qwen2_audio |
| └─ MiniCPM-o-2.6 | 8B | 音频+图像+文本 | minicpm_o |

## 3. 训练方法

### 3.1 训练方法对比

| 训练方法 | 全参数训练 | 部分参数训练 | LoRA | QLoRA |
|---------|-----------|-------------|------|-------|
| **预训练** | ✅ | ✅ | ✅ | ✅ |
| **指令监督微调** | ✅ | ✅ | ✅ | ✅ |
| **奖励模型训练** | ✅ | ✅ | ✅ | ✅ |
| **PPO 训练** | ✅ | ✅ | ✅ | ✅ |
| **DPO 训练** | ✅ | ✅ | ✅ | ✅ |
| **KTO 训练** | ✅ | ✅ | ✅ | ✅ |
| **ORPO 训练** | ✅ | ✅ | ✅ | ✅ |
| **SimPO 训练** | ✅ | ✅ | ✅ | ✅ |

### 3.2 硬件需求估算

| 训练方法 | 精度 | 7B | 14B | 30B | 70B |
|---------|------|----|----|----|----|
| **全参数训练** |
| ├─ bf16/fp16 | 32位 | 120GB | 240GB | 600GB | 1200GB |
| └─ pure_bf16 | 16位 | 60GB | 120GB | 300GB | 600GB |
| **参数高效训练** |
| ├─ Freeze/LoRA | 16位 | 16GB | 32GB | 64GB | 160GB |
| ├─ QLoRA (8bit) | 8位 | 10GB | 20GB | 40GB | 80GB |
| ├─ QLoRA (4bit) | 4位 | 6GB | 12GB | 24GB | 48GB |
| └─ QLoRA (2bit) | 2位 | 4GB | 8GB | 16GB | 24GB |

## 4. 数据集

### 4.1 预训练数据集
- **英文数据**：RefinedWeb、RedPajama V2、Wikipedia、Pile、FineWeb、The Stack
- **中文数据**：Wikipedia (zh)、SkyPile、StarCoder

### 4.2 指令微调数据集
- **通用指令**：Stanford Alpaca、LIMA、Guanaco、BELLE、UltraChat
- **代码指令**：CodeAlpaca、Evol Instruct
- **数学指令**：MathInstruct、BELLE School Math
- **多模态指令**：LLaVA mixed、Pokemon-gpt4o-captions

### 4.3 偏好数据集
- **对话偏好**：DPO mixed、UltraFeedback、HH-RLHF
- **多模态偏好**：RLHF-V、VLFeedback、RLAIF-V

## 5. 环境要求

### 5.1 软件依赖

#### 必需组件
| 组件 | 最低版本 | 推荐版本 |
|------|---------|---------|
| Python | 3.9 | 3.10 |
| PyTorch | 2.0.0 | 2.6.0 |
| Transformers | 4.49.0 | 4.50.0 |
| Datasets | 2.16.0 | 3.2.0 |
| Accelerate | 0.34.0 | 1.2.1 |
| PEFT | 0.14.0 | 0.15.1 |
| TRL | 0.8.6 | 0.9.6 |

#### 可选组件
| 组件 | 最低版本 | 推荐版本 | 用途 |
|------|---------|---------|------|
| CUDA | 11.6 | 12.2 | GPU 加速 |
| DeepSpeed | 0.10.0 | 0.16.4 | 分布式训练 |
| BitsAndBytes | 0.39.0 | 0.43.1 | 量化训练 |
| vLLM | 0.4.3 | 0.8.2 | 高速推理 |
| Flash-Attention | 2.5.6 | 2.7.2 | 注意力加速 |

### 5.2 硬件要求

#### GPU 推荐配置
- **入门级**：RTX 3090/4090 (24GB) - 适合 7B 模型 QLoRA 微调
- **专业级**：A100 (40GB/80GB) - 适合 13B-70B 模型训练
- **企业级**：H100 (80GB) - 适合大规模模型和分布式训练

#### 内存要求
- **最低配置**：32GB 系统内存
- **推荐配置**：64GB+ 系统内存（大模型训练）

## 6. 安装指南

### 6.1 从源码安装（推荐）

```bash
# 1. 克隆仓库
git clone --depth 1 https://github.com/hiyouga/LLaMA-Factory.git
cd LLaMA-Factory

# 2. 安装依赖
pip install -e ".[torch,metrics]" --no-build-isolation
```

#### 可选依赖项
```bash
# 基础安装
pip install -e ".[torch,metrics]"

# 完整安装（包含所有功能）
pip install -e ".[torch,metrics,deepspeed,bitsandbytes,vllm,liger-kernel]"
```

### 6.2 Docker 安装

#### 快速启动
```bash
# CUDA 用户
docker run -it --rm --gpus=all --ipc=host hiyouga/llamafactory:latest

# 查看所有可用镜像
# https://hub.docker.com/r/hiyouga/llamafactory/tags
```

#### 自定义构建
```bash
# 进入 Docker 目录
cd docker/docker-cuda/

# 启动服务
docker compose up -d

# 进入容器
docker compose exec llamafactory bash
```

### 6.3 特殊平台安装

#### Windows 用户
```bash
# 1. 安装 PyTorch (GPU 版本)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu126

# 2. 验证 CUDA 可用性
python -c "import torch; print(torch.cuda.is_available())"

# 3. 安装 LLaMA Factory
pip install -e ".[torch,metrics]" --no-build-isolation
```

#### 昇腾 NPU 用户
```bash
# 1. 安装 NPU 版本
pip install -e ".[torch-npu,metrics]"

# 2. 安装 CANN Toolkit
# 详细步骤请参考官方文档

# 3. 设置环境变量
export ASCEND_RT_VISIBLE_DEVICES=0,1,2,3
```

## 7. 快速开始

### 7.1 三步快速体验

```bash
# 1. 微调模型
llamafactory-cli train examples/train_lora/llama3_lora_sft.yaml

# 2. 对话测试
llamafactory-cli chat examples/inference/llama3_lora_sft.yaml

# 3. 合并模型
llamafactory-cli export examples/merge_lora/llama3_lora_sft.yaml
```

### 7.2 Web UI 可视化微调

```bash
# 启动 Web 界面
llamafactory-cli webui

# 浏览器访问：http://localhost:7860
```

### 7.3 数据准备

#### 数据格式要求
```json
[
  {
    "instruction": "用户指令",
    "input": "输入内容（可选）",
    "output": "期望输出"
  }
]
```

#### 自定义数据集
1. 准备数据文件（JSON 格式）
2. 更新 `data/dataset_info.json`
3. 在训练配置中指定数据集名称

### 7.4 配置文件说明

#### 基础训练配置
```yaml
# 模型配置
model_name_or_path: meta-llama/Llama-3-8B-Instruct
template: llama3

# 数据配置
dataset: alpaca_zh
cutoff_len: 1024

# 训练配置
stage: sft
do_train: true
finetuning_type: lora
lora_target: all

# 输出配置
output_dir: ./saves/llama3-8b/lora/sft
logging_steps: 10
save_steps: 500
```

## 8. 高级功能

### 8.1 分布式训练

#### DeepSpeed 配置
```bash
# 使用 DeepSpeed ZeRO-3
llamafactory-cli train examples/deepspeed/llama3_full_sft_ds3.yaml
```

#### 多 GPU 训练
```bash
# 使用 Accelerate
accelerate launch --config_file examples/accelerate/fsdp_config.yaml \
    src/train.py examples/train_full/llama3_full_sft.yaml
```

### 8.2 量化训练

#### QLoRA 配置
```yaml
# 4-bit 量化
quantization_bit: 4
quantization_type: nf4
double_quantization: true

# LoRA 配置
finetuning_type: lora
lora_rank: 64
lora_alpha: 16
lora_dropout: 0.1
```

### 8.3 多模态训练

#### 视觉模型微调
```yaml
# LLaVA 模型配置
model_name_or_path: llava-hf/llava-1.5-7b-hf
template: llava
dataset: llava_mixed

# 多模态数据格式
# {
#   "messages": [...],
#   "images": ["image1.jpg", "image2.jpg"]
# }
```

### 8.4 偏好对齐训练

#### DPO 训练配置
```yaml
stage: dpo
dataset: dpo_mixed
dpo_beta: 0.1
dpo_loss: sigmoid
```

## 9. 部署指南

### 9.1 API 服务部署

#### vLLM 后端
```bash
# 启动 API 服务
API_PORT=8000 llamafactory-cli api \
    examples/inference/llama3.yaml \
    infer_backend=vllm \
    vllm_enforce_eager=true
```

#### 测试 API
```python
import openai

client = openai.OpenAI(
    api_key="EMPTY",
    base_url="http://localhost:8000/v1"
)

response = client.chat.completions.create(
    model="llama3",
    messages=[{"role": "user", "content": "你好"}]
)
print(response.choices[0].message.content)
```

### 9.2 模型导出

#### 合并 LoRA 权重
```bash
llamafactory-cli export examples/merge_lora/llama3_lora_sft.yaml
```

#### 导出为 GGUF 格式
```bash
# 安装 llama.cpp
git clone https://github.com/ggerganov/llama.cpp
cd llama.cpp && make

# 转换模型
python convert.py /path/to/merged/model
```

### 9.3 生产环境部署

#### Docker Compose 部署
```yaml
version: '3.8'
services:
  llamafactory:
    image: hiyouga/llamafactory:latest
    ports:
      - "8000:8000"
    volumes:
      - ./models:/app/models
      - ./data:/app/data
    environment:
      - CUDA_VISIBLE_DEVICES=0,1
    command: >
      llamafactory-cli api
      --model_name_or_path /app/models/llama3-8b-sft
      --template llama3
      --infer_backend vllm
```

## 10. 常见问题

### 10.1 安装问题

**Q: 安装时出现 CUDA 版本不匹配？**
```bash
# 检查 CUDA 版本
nvidia-smi

# 安装对应版本的 PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

**Q: Windows 下无法安装 BitsAndBytes？**
```bash
# 使用预编译版本
pip install https://github.com/jllllll/bitsandbytes-windows-webui/releases/download/wheels/bitsandbytes-0.41.2.post2-py3-none-win_amd64.whl
```

### 10.2 训练问题

**Q: 显存不足怎么办？**
- 使用更小的 batch_size
- 启用梯度检查点：`gradient_checkpointing: true`
- 使用量化训练：`quantization_bit: 4`
- 使用 DeepSpeed ZeRO：`deepspeed: examples/deepspeed/ds_z3_config.json`

**Q: 训练速度慢？**
- 启用 Flash Attention：`flash_attn: fa2`
- 使用 Unsloth 加速：`use_unsloth: true`
- 启用 Liger Kernel：`enable_liger_kernel: true`

### 10.3 推理问题

**Q: 推理结果不理想？**
- 检查模板是否正确
- 调整生成参数：`temperature`、`top_p`、`max_new_tokens`
- 确保训练和推理使用相同配置

---

## 📞 联系我们

- **GitHub Issues**: [提交问题](https://github.com/hiyouga/LLaMA-Factory/issues)
- **微信群**: [加入讨论](assets/wechat.jpg)
- **文档**: [详细文档](https://llamafactory.readthedocs.io/zh-cn/latest/)

## 📄 许可证

本项目采用 [Apache 2.0](LICENSE) 许可证。

## 🙏 致谢

感谢所有贡献者和支持者！特别感谢 Hugging Face、Microsoft、NVIDIA 等组织提供的开源工具和资源。
