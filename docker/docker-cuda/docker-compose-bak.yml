services:
  llamafactory:
    build:
      dockerfile: ./docker/docker-cuda/Dockerfile
      context: ../..
      args:
        PIP_INDEX: https://pypi.org/simple
        EXTRAS: metrics
    container_name: llamafactory
    ports:
      - "7861:7860"
      - "8002:8000"
    ipc: host
    tty: true
    # shm_size: "16gb"  # ipc: host is set
    stdin_open: true
    #修改为如下命令，即可自动启动web服务
    #command: bash
    command: llamafactory-cli webui
    deploy:
      resources:
        reservations:
          devices:
          - driver: nvidia
            count: "all"
            capabilities: [ gpu ]
    restart: unless-stopped
