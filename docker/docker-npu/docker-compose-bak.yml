services:
  llamafactory:
    build:
      dockerfile: ./docker/docker-cuda/Dockerfile
      context: ../..
      args:
        PIP_INDEX: https://pypi.org/simple
        EXTRAS: metrics
    container_name: llamafactory
    ports:
      - "7860:7860"
      - "8000:8000"
    volumes:
      # 必须映射的路径
      - ./huggingface_cache:/root/.cache/huggingface
      - ./saves:/app/saves
      - ./data:/app/data

      # 强烈推荐映射的路径
      - ./config:/app/config
      - ./cache:/app/cache
      - ./exports:/app/exports

      # 可选映射的路径
      - ./logs:/app/logs
      - ./tmp:/app/tmp

      # 如果有自定义模型
      - ./models:/app/models

    environment:
      - GRADIO_SERVER_NAME=0.0.0.0
      - GRADIO_SERVER_PORT=7860
      - HF_HOME=/root/.cache/huggingface

    ipc: host
    tty: true
    stdin_open: true
    command: llamafactory-cli webui
    deploy:
      resources:
        reservations:
          devices:
          - driver: nvidia
            count: "all"
            capabilities: [ gpu ]
    restart: unless-stopped