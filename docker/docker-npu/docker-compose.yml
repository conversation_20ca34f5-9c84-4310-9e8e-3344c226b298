services:
  llamafactory:
    build:
      dockerfile: ./docker/docker-npu/Dockerfile
      context: ../..
      args:
        PIP_INDEX: https://pypi.org/simple
        EXTRAS: torch-npu,metrics
    container_name: llamafactory
    volumes:
      # NPU 特定的系统挂载
      - /usr/local/dcmi:/usr/local/dcmi
      - /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
      - /usr/local/Ascend/driver:/usr/local/Ascend/driver
      - /etc/ascend_install.info:/etc/ascend_install.info

      # 数据持久化挂载
      - ./huggingface_cache:/root/.cache/huggingface
      - ./saves:/app/saves
      - ./data:/app/data
      - ./config:/app/config
      - ./cache:/app/cache
      - ./exports:/app/exports
      - ./logs:/app/logs
      - ./models:/app/models

    ports:
      - "7860:7860"
      - "8000:8000"
    environment:
      - GRADIO_SERVER_NAME=0.0.0.0
      - HF_HOME=/root/.cache/huggingface

    ipc: host
    tty: true
    stdin_open: true
    command: llamafactory-cli webui
    devices:
      - /dev/davinci0
      - /dev/davinci_manager
      - /dev/devmm_svm
      - /dev/hisi_hdc
    restart: unless-stopped