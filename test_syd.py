import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('TkAgg')  # 或者 'QtAgg'、'Agg'，具体看系统支持情况

def positional_encoding_matrix(seq_len, d_model):
    """
    生成 Transformer 原始正余弦位置编码矩阵，shape: [seq_len, d_model]
    """
    position = np.arange(seq_len).reshape(-1, 1)        # [T, 1]
    dim = np.arange(d_model).reshape(1, -1)              # [1, D]

    # k = i//2，用来计算频率分母
    angle_rates = 1 / (10000 ** (2 * (dim // 2) / d_model))  # shape: [1, D]
    angle_rads = position * angle_rates                   # shape: [T, D]

    # 应用 sin 到偶数维，cos 到奇数维
    pe = np.zeros_like(angle_rads)
    pe[:, 0::2] = np.sin(angle_rads[:, 0::2])  # even i
    pe[:, 1::2] = np.cos(angle_rads[:, 1::2])  # odd i
    return pe

def plot_pe_heatmap(seq_len=100, d_model=64):
    pe = positional_encoding_matrix(seq_len, d_model)

    plt.figure(figsize=(12, 6))
    plt.imshow(pe.T, aspect='auto', cmap='coolwarm', origin='lower')
    plt.colorbar(label='Encoding Value')
    plt.xlabel('Position $t$')
    plt.ylabel('Dimension $i$')
    plt.title(f'Transformer Positional Encoding Heatmap (d_model={d_model})')
    plt.tight_layout()
    plt.show()

# 可视化 d_model=64 的位置编码热图
plot_pe_heatmap(seq_len=100000, d_model=640)
